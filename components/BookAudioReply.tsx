'use client'

import { useState } from 'react'
import Image from 'next/image'
import { AudioPlayer } from './AudioPlayer'
import { AudioRecorder } from './AudioRecorder'
import { ReactionSystem } from './ReactionSystem'
import { MessageCircle, User } from 'lucide-react'
import { Day1Badge } from './Day1Badge'

interface BookAudioReply {
  id: string
  user_id: string
  audio_url: string
  duration_seconds: number
  love_count: number
  parent_reply_id?: string | null
  created_at: string
  reaction_counts?: Record<string, number>
  userReaction?: string | null
  user?: {
    id: string
    name: string
    avatar?: string
    profile_picture_url?: string
    has_day1_badge?: boolean
    signup_number?: number
  }
}

interface BookAudioReplyProps {
  reply: BookAudioReply
  bookId: string
  currentUserId?: string
  onReply?: (parentReplyId: string) => void
  onNestedReply?: (parentReplyId: string, audioBlob: Blob, duration: number) => Promise<void>
  level?: number // For nested replies
  maxNestingLevel?: number
}

export function BookAudioReply({
  reply,
  bookId,
  currentUserId,
  onReply,
  onNestedReply,
  level = 0,
  maxNestingLevel = 3
}: BookAudioReplyProps) {
  const [reactions, setReactions] = useState(reply.reaction_counts || {})
  const [userReaction, setUserReaction] = useState(reply.userReaction)
  const [showReplyRecorder, setShowReplyRecorder] = useState(false)

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) return 'just now'
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
    return `${Math.floor(diffInSeconds / 86400)}d ago`
  }

  const handleReply = () => {
    if (level < maxNestingLevel) {
      setShowReplyRecorder(true)
    } else {
      onReply?.(reply.id)
    }
  }

  const handleReactionUpdate = (newReactions: Record<string, number>, newUserReaction: string | null) => {
    setReactions(newReactions)
    setUserReaction(newUserReaction)
  }

  const handleAudioReply = async (audioBlob: Blob, duration: number) => {
    try {
      await onNestedReply?.(reply.id, audioBlob, duration)
      setShowReplyRecorder(false)
    } catch (error) {
      console.error('Error creating nested reply:', error)
      alert('Failed to create reply. Please try again.')
    }
  }

  // Calculate threading shade based on level
  const getThreadingShade = (level: number) => {
    const shades = [
      'border-l-gray-200 bg-white',
      'border-l-blue-200 bg-blue-50/30',
      'border-l-purple-200 bg-purple-50/30',
      'border-l-pink-200 bg-pink-50/30'
    ]
    return shades[Math.min(level, shades.length - 1)]
  }

  // Add fire emojis for deep threading
  const getFireEmojis = (level: number) => {
    if (level >= 3) return '🔥🔥🔥'
    if (level >= 2) return '🔥🔥'
    if (level >= 1) return '🔥'
    return ''
  }

  // Get threading padding based on level
  const getThreadingPadding = (level: number) => {
    if (level === 0) return ''
    return `border-l-4 ${getThreadingShade(level)} pl-4 sm:pl-6 ml-4 sm:ml-6 md:ml-8 relative`
  }

  return (
    <div className={getThreadingPadding(level)}>
      {/* Visual nesting indicator */}
      {level > 0 && (
        <div className="absolute -left-2 top-4 w-4 h-4 bg-blue-200 rounded-full border-2 border-white shadow-sm z-10"></div>
      )}

      <div className="bg-white rounded-lg p-3 sm:p-4 mb-3 shadow-sm border border-gray-100">
        {/* Reply indicator */}
        {level > 0 && (
          <div className="flex items-center gap-2 mb-2 text-xs">
            <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded-full font-medium">
              ↳ Reply {getFireEmojis(level)}
            </span>
            {level >= 2 && <span className="text-purple-600 font-medium">Level {level}</span>}
          </div>
        )}

        {/* User Info */}
        <div className="flex items-center gap-2 sm:gap-3 mb-2 sm:mb-3">
          {reply.user?.profile_picture_url || reply.user?.avatar ? (
            <div className="relative">
              <Image
                src={reply.user.profile_picture_url || reply.user.avatar || ''}
                alt={reply.user.name}
                width={32}
                height={32}
                className="w-6 h-6 sm:w-8 sm:h-8 rounded-full object-cover"
              />
              {reply.user.has_day1_badge && (
                <Day1Badge 
                  className="absolute -top-1 -right-1 w-3 h-3 sm:w-4 sm:h-4" 
                  size="xs"
                />
              )}
            </div>
          ) : (
            <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-gray-400 flex items-center justify-center">
              <User className="w-3 h-3 sm:w-4 sm:h-4 text-white" />
            </div>
          )}
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-1 sm:gap-2">
              <p className="font-medium text-xs sm:text-sm text-gray-900 truncate">
                {reply.user?.name || 'Anonymous'}
              </p>
              {level > 0 && (
                <div className="flex items-center gap-1">
                  <span className="text-xs text-gray-400">•</span>
                  <span className="text-xs text-gray-500">L{level}</span>
                  {getFireEmojis(level) && (
                    <span className="text-xs">{getFireEmojis(level)}</span>
                  )}
                </div>
              )}
            </div>
            <p className="text-xs text-gray-500">{formatTimeAgo(reply.created_at)}</p>
          </div>
        </div>

        {/* Audio Player */}
        <div className="mb-3">
          <AudioPlayer
            audioUrl={reply.audio_url}
            duration={reply.duration_seconds}
            className="w-full"
          />
        </div>

        {/* Actions */}
        <div className="flex items-center gap-2 sm:gap-4">
          {/* Reaction System */}
          <ReactionSystem
            contentType="book_audio_reply"
            contentId={reply.id}
            bookId={bookId}
            currentUserId={currentUserId}
            initialReactions={reactions}
            userReaction={userReaction}
            onReactionUpdate={handleReactionUpdate}
          />

          {level < maxNestingLevel && (
            <button
              onClick={handleReply}
              disabled={!currentUserId}
              className={`flex items-center gap-1 sm:gap-2 px-2 sm:px-3 py-2 rounded-lg transition-all text-blue-600 hover:bg-blue-50 min-h-[44px] ${
                !currentUserId ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              <MessageCircle className="w-3 h-3 sm:w-4 sm:h-4" />
              <span className="text-xs sm:text-sm font-medium">Reply</span>
            </button>
          )}
        </div>

        {/* Nested Reply Recorder */}
        {showReplyRecorder && (
          <div className="mt-4 p-3 bg-gray-50 rounded-lg">
            <div className="mb-2">
              <p className="text-sm font-medium text-gray-700">Reply to {reply.user?.name}</p>
            </div>
            <AudioRecorder
              onRecordingComplete={handleAudioReply}
              onCancel={() => setShowReplyRecorder(false)}
              maxDuration={9}
              className="w-full"
            />
          </div>
        )}
      </div>
    </div>
  )
}
